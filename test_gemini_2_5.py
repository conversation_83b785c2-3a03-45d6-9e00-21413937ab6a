#!/usr/bin/env python3
"""
测试 Gemini 2.5 模型调用
验证 Roo Code 插件配置是否正确
"""

import requests
import json
import time
import socket
from urllib.parse import urlparse

def check_network_connectivity(url: str, timeout: int = 10) -> dict:
    """检查网络连通性"""
    result = {
        "dns_resolution": False,
        "tcp_connection": False,
        "http_response": False,
        "response_time": None,
        "error_message": None
    }

    try:
        # 解析URL
        parsed = urlparse(url)
        hostname = parsed.hostname
        port = parsed.port or (443 if parsed.scheme == 'https' else 80)

        print(f"🔍 网络诊断: {hostname}:{port}")

        # 1. DNS解析测试
        try:
            start_time = time.time()
            ip = socket.gethostbyname(hostname)
            dns_time = time.time() - start_time
            result["dns_resolution"] = True
            print(f"   ✅ DNS解析成功: {hostname} -> {ip} ({dns_time:.2f}s)")
        except socket.gaierror as e:
            result["error_message"] = f"DNS解析失败: {e}"
            print(f"   ❌ DNS解析失败: {e}")
            return result

        # 2. TCP连接测试
        try:
            start_time = time.time()
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            sock.connect((ip, port))
            tcp_time = time.time() - start_time
            sock.close()
            result["tcp_connection"] = True
            print(f"   ✅ TCP连接成功 ({tcp_time:.2f}s)")
        except socket.error as e:
            result["error_message"] = f"TCP连接失败: {e}"
            print(f"   ❌ TCP连接失败: {e}")
            return result

        # 3. HTTP响应测试
        try:
            start_time = time.time()
            response = requests.head(url, timeout=timeout)
            http_time = time.time() - start_time
            result["http_response"] = True
            result["response_time"] = http_time
            print(f"   ✅ HTTP响应成功: {response.status_code} ({http_time:.2f}s)")
        except requests.RequestException as e:
            result["error_message"] = f"HTTP请求失败: {e}"
            print(f"   ❌ HTTP请求失败: {e}")

    except Exception as e:
        result["error_message"] = f"网络检查异常: {e}"
        print(f"   ❌ 网络检查异常: {e}")

    return result

def test_gemini_2_5():
    """测试 Gemini 2.5 模型"""

    # 配置信息
    BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"

    # 不同的超时配置
    timeout_configs = [30, 60, 120]  # 30秒, 60秒, 120秒

    # 测试的 Gemini 2.5 模型
    models_to_test = [
        "gemini-2.5-pro",
        "gemini-2.5-flash",
        "gemini-2.5-pro-preview-03-25",
        "gemini-2.5-flash-preview-05-20"
    ]

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }

    print("🧪 测试 Gemini 2.5 模型")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print(f"认证令牌: {AUTH_TOKEN}")
    print()

    # 先进行网络连通性检查
    print("🌐 网络连通性检查")
    print("-" * 30)
    connectivity = check_network_connectivity(BASE_URL)

    if not connectivity["tcp_connection"]:
        print("❌ 网络连接失败，无法继续测试")
        print(f"错误信息: {connectivity['error_message']}")
        return

    print()

    # 测试不同超时配置
    for timeout in timeout_configs:
        print(f"⏱️  测试超时配置: {timeout}秒")
        print("-" * 30)

        success_count = 0
        for model in models_to_test:
            print(f"🤖 测试模型: {model}")

            data = {
                "model": model,
                "messages": [
                    {"role": "user", "content": "请简单介绍一下你自己，并说明你的版本"}
                ],
                "max_tokens": 200,
                "temperature": 0.7
            }

            try:
                start_time = time.time()
                response = requests.post(
                    f"{BASE_URL}/v1/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=timeout
                )
                response_time = time.time() - start_time

                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    print(f"   ✅ 成功 ({response_time:.2f}s): {content[:50]}...")
                    success_count += 1
                else:
                    print(f"   ❌ 失败: HTTP {response.status_code}")
                    print(f"   错误: {response.text[:100]}...")

            except requests.exceptions.Timeout:
                print(f"   ⏰ 超时: 请求超过 {timeout} 秒")
            except requests.exceptions.ConnectionError as e:
                print(f"   🔌 连接错误: {e}")
            except Exception as e:
                print(f"   ❌ 请求失败: {e}")

            print()

        print(f"📊 超时 {timeout}s 测试结果: {success_count}/{len(models_to_test)} 成功")
        print()

        # 如果有成功的请求，就不需要测试更长的超时
        if success_count > 0:
            break

    # 测试代码生成能力（仅在有成功连接时）
    if success_count > 0:
        print("💻 测试代码生成能力 (gemini-2.5-pro)")
        print("-" * 30)

        code_data = {
            "model": "gemini-2.5-pro",
            "messages": [
                {"role": "user", "content": "请写一个Python函数，计算斐波那契数列的第n项"}
            ],
            "max_tokens": 500,
            "temperature": 0.3
        }

        try:
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}/v1/chat/completions",
                headers=headers,
                json=code_data,
                timeout=60  # 代码生成使用更长超时
            )
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"✅ 代码生成成功 ({response_time:.2f}s):")
                print(content)
            else:
                print(f"❌ 代码生成失败: HTTP {response.status_code}")
                print(f"错误: {response.text}")

        except requests.exceptions.Timeout:
            print("⏰ 代码生成超时")
        except Exception as e:
            print(f"❌ 代码生成请求失败: {e}")
    else:
        print("⚠️  跳过代码生成测试（无可用连接）")

def test_alternative_endpoints():
    """测试备用端点"""
    print("\n" + "=" * 50)
    print("🔄 测试备用端点")
    print("=" * 50)

    # 一些常见的备用端点
    alternative_endpoints = [
        "https://api.openai.com",  # OpenAI 官方
        "https://api.anthropic.com",  # Anthropic 官方
        "https://generativelanguage.googleapis.com",  # Google 官方
    ]

    for endpoint in alternative_endpoints:
        print(f"🔍 测试端点: {endpoint}")
        connectivity = check_network_connectivity(endpoint, timeout=10)
        if connectivity["tcp_connection"]:
            print(f"   ✅ 连接正常")
        else:
            print(f"   ❌ 连接失败: {connectivity['error_message']}")
        print()

def generate_troubleshooting_guide():
    """生成故障排除指南"""
    print("\n" + "=" * 50)
    print("🛠️  故障排除指南")
    print("=" * 50)

    print("如果遇到连接超时问题，请尝试以下解决方案：")
    print()
    print("1. 🌐 网络问题:")
    print("   - 检查网络连接是否正常")
    print("   - 尝试使用VPN或代理")
    print("   - 检查防火墙设置")
    print()
    print("2. ⏱️  超时设置:")
    print("   - 增加超时时间到60-120秒")
    print("   - 在网络较慢时使用更长超时")
    print()
    print("3. 🔧 服务器问题:")
    print("   - 服务器可能暂时不可用")
    print("   - 检查服务器状态页面")
    print("   - 联系服务提供商")
    print()
    print("4. 🔑 认证问题:")
    print("   - 检查API密钥是否正确")
    print("   - 确认账户是否有效")
    print("   - 检查配额是否用完")

def generate_roo_code_config():
    """生成 Roo Code 配置示例"""

    print("\n" + "=" * 50)
    print("🔧 Roo Code 插件配置")
    print("=" * 50)

    # 基础配置
    config = {
        "provider": "openai",
        "apiKey": "12345",
        "baseURL": "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com/v1",
        "model": "gemini-2.5-pro",
        "temperature": 0.7,
        "maxTokens": 4096,
        "stream": True
    }

    # 针对超时问题的优化配置
    optimized_config = {
        "provider": "openai",
        "apiKey": "12345",
        "baseURL": "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com/v1",
        "model": "gemini-2.5-flash",  # 使用更快的模型
        "temperature": 0.7,
        "maxTokens": 2048,  # 减少token数量
        "stream": True,
        "timeout": 60000,  # 60秒超时（毫秒）
        "retries": 3  # 重试次数
    }

    print("📋 基础配置:")
    print(json.dumps(config, indent=2, ensure_ascii=False))

    print("\n🚀 优化配置（针对超时问题）:")
    print(json.dumps(optimized_config, indent=2, ensure_ascii=False))

    print("\n🎯 推荐模型选择:")
    print("- gemini-2.5-pro: 最强性能，适合复杂代码生成")
    print("- gemini-2.5-flash: 快速响应，适合简单任务（推荐）")
    print("- gemini-2.5-pro-preview-03-25: 预览版本，可能有新功能")

    print("\n⚙️ 参数说明:")
    print("- temperature: 0.3-0.7 适合代码生成")
    print("- maxTokens: 建议 1024-2048（减少超时风险）")
    print("- stream: 启用流式输出，提升体验")
    print("- timeout: 设置较长超时时间（60秒）")
    print("- retries: 设置重试次数（3次）")

if __name__ == "__main__":
    try:
        test_gemini_2_5()
        test_alternative_endpoints()
        generate_roo_code_config()
        generate_troubleshooting_guide()
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
